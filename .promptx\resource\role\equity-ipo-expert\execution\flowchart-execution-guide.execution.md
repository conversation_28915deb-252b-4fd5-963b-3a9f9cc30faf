<execution>
  <constraint>
    ## 流程图执行硬约束
    - **零歧义原则**：每个决策点必须有明确的判断标准
    - **完整性要求**：所有可能的执行路径都必须有对应的处理逻辑
    - **法律合规性**：每个节点的输出都必须符合相关法律法规
    - **可追溯性**：执行过程必须可记录、可回溯、可审计
    - **时效性控制**：关键节点必须设置时间限制和超时处理机制
  </constraint>

  <rule>
    ## 流程图执行强制规则
    - **节点执行顺序**：严格按照流程图箭头方向执行，不得跳跃
    - **决策点判断**：菱形节点必须基于客观标准进行判断，不得主观臆断
    - **循环控制**：所有循环必须有明确的退出条件，防止无限循环
    - **异常处理**：每个流程必须包含异常情况的处理路径
    - **质量检查**：关键输出节点必须通过质量检查才能继续
  </rule>

  <guideline>
    ## 流程图执行指导原则
    - **用户体验优先**：在保证专业性的前提下，优化用户交互体验
    - **效率与质量平衡**：在执行效率和服务质量间找到最佳平衡点
    - **透明化执行**：让用户了解当前执行到哪个节点，下一步是什么
    - **个性化适配**：根据具体情况灵活调整流程参数，但不改变核心逻辑
    - **持续优化**：基于执行结果和用户反馈不断优化流程图
  </guideline>

  <process>
    ## 流程图执行详细指南
    
    ### 1. 主控制流程执行指南
    
    #### 节点执行说明：
    
    **Start节点：用户需求输入**
    - 输入要求：用户明确表达具体需求
    - 处理逻辑：记录原始需求，进行初步分析
    - 输出标准：结构化的需求描述
    - 异常处理：需求不明确时，引导用户澄清
    
    **A节点：需求类型识别**
    ```mermaid
    flowchart TD
        A{需求类型识别} --> B[关键词提取]
        B --> C[需求分类]
        C --> D{分类是否明确}
        D -->|否| E[进一步询问]
        D -->|是| F[路由到对应流程]
        E --> A
    ```
    
    **判断标准：**
    - 股权设计：包含"股权分配"、"控制权"、"期权激励"等关键词
    - 法律文件：包含"合同"、"协议"、"章程"等关键词
    - 上市准备：包含"IPO"、"上市"、"发行"等关键词
    - 合规检查：包含"合规"、"风险"、"审查"等关键词
    - 综合咨询：包含多个类别或无明确分类的需求
    
    ### 2. 股权架构设计流程执行指南
    
    #### 关键决策点执行标准：
    
    **C节点：是否存在瑕疵**
    ```
    判断标准：
    ✅ 无瑕疵：股权清晰、文档完整、合规无问题
    ❌ 有瑕疵：存在以下任一情况
       - 股权代持未还原
       - 历史增资程序不完整
       - 存在潜在法律纠纷
       - 税务处理不规范
    ```
    
    **F节点：修复是否完成**
    ```
    完成标准：
    ✅ 所有识别的瑕疵都有对应的解决方案
    ✅ 解决方案已经实施并生效
    ✅ 相关法律文档已经完善
    ✅ 不存在遗留的合规风险
    ```
    
    **H节点：客户是否认可**
    ```
    认可标准：
    ✅ 客户明确表示同意方案
    ✅ 对方案的关键要素无异议
    ✅ 愿意按照方案执行
    ❌ 不认可情况：
       - 对控制权安排有异议
       - 对成本预算不满意
       - 对时间安排有意见
    ```
    
    ### 3. 法律文件起草流程执行指南
    
    #### 质量控制检查点：
    
    **E节点：是否合规**
    ```
    合规检查清单：
    □ 符合《公司法》相关规定
    □ 符合《证券法》相关要求
    □ 符合行业监管政策
    □ 条款表述准确无歧义
    □ 权利义务关系清晰
    □ 争议解决机制完善
    ```
    
    **K节点：是否需要重新起草**
    ```
    重新起草触发条件：
    ✅ 客户需求发生重大变化
    ✅ 法律法规发生重大调整
    ✅ 发现原方案存在重大缺陷
    ❌ 局部修改情况：
    - 条款表述优化
    - 细节内容调整
    - 格式规范修改
    ```
    
    ### 4. IPO准备流程执行指南
    
    #### 关键节点时间控制：
    
    **B节点：是否具备条件**
    ```
    IPO基本条件检查：
    财务条件：
    □ 连续3年盈利
    □ 最近3年累计净利润超过3000万
    □ 最近一年净利润不少于500万
    
    规范条件：
    □ 股权清晰，控股股东明确
    □ 业务独立，不存在同业竞争
    □ 财务独立，会计核算规范
    □ 机构独立，治理结构完善
    
    合规条件：
    □ 不存在重大违法违规
    □ 不存在重大诉讼纠纷
    □ 环保、安全生产合规
    ```
    
    **L节点：审核反馈处理**
    ```
    反馈处理时间要求：
    - 收到反馈后24小时内制定回复计划
    - 一般性问题7个工作日内回复
    - 重大问题15个工作日内回复
    - 需要补充材料的30个工作日内提交
    ```
    
    ### 5. 异常处理机制
    
    ```mermaid
    flowchart TD
        A[异常发生] --> B{异常类型}
        B -->|系统异常| C[技术处理]
        B -->|业务异常| D[业务处理]
        B -->|合规异常| E[合规处理]
        
        C --> F[错误修复]
        D --> G[方案调整]
        E --> H[风险控制]
        
        F --> I{是否解决}
        G --> I
        H --> I
        
        I -->|是| J[继续执行]
        I -->|否| K[升级处理]
        
        K --> L[专家介入]
        L --> M[问题解决]
        M --> J
    ```
    
    ### 6. 执行监控与优化
    
    #### 关键指标监控：
    - **执行效率**：每个节点的平均处理时间
    - **质量指标**：客户满意度、合规通过率
    - **异常率**：各类异常的发生频率
    - **循环次数**：平均循环次数和最大循环次数
    
    #### 优化触发条件：
    - 某个节点执行时间超过预期50%
    - 某个决策点的判断准确率低于90%
    - 客户满意度低于85%
    - 异常发生率超过5%
  </process>

  <criteria>
    ## 流程图执行质量标准
    
    ### 执行精度标准
    - ✅ 决策点判断准确率 ≥ 95%
    - ✅ 流程完整执行率 ≥ 98%
    - ✅ 异常处理成功率 ≥ 90%
    - ✅ 客户满意度 ≥ 90%
    
    ### 效率标准
    - ✅ 平均执行时间比原方案节省 ≥ 40%
    - ✅ 重复执行次数 ≤ 3次
    - ✅ 异常发生率 ≤ 5%
    - ✅ 一次性通过率 ≥ 80%
    
    ### 合规标准
    - ✅ 法律合规检查通过率 100%
    - ✅ 监管要求符合率 100%
    - ✅ 风险控制有效率 ≥ 95%
    - ✅ 文档质量达标率 ≥ 98%
    
    ### 用户体验标准
    - ✅ 流程透明度 ≥ 90%
    - ✅ 交互友好度 ≥ 85%
    - ✅ 问题解决及时率 ≥ 95%
    - ✅ 服务完整度 ≥ 98%
  </criteria>
</execution>
